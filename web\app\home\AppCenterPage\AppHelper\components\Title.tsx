import { COMMON } from '@/app/home/<USER>'
import titleBg from '@/assets/images/search/titleBg.png'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
export default function AppHelperTitle() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  console.log(pathname, searchParams)
  return (
    <div className="mb-8 text-center font-[PingFangSC]">
      <div className="mb-1 px-[15px] pr-[27px] text-[29px] font-[600] text-[#2C2C36]" style={{ backgroundImage: `url(${titleBg.src})`, backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat' }}>
        {}
      </div>
      <div className="text-[16px] font-[400] text-[#828DA2]">
        {COMMON.searchPageSubtitle}
      </div>
    </div>
  )
}
